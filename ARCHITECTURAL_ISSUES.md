# WavyAudio: Architectural and Runtime Issues Audit

Based on an analysis of the project's source code and official Apple Developer Documentation, the following potential build, run, and stability issues have been identified.

### 1. Critical Issue: Flawed Per-App Volume Control Implementation

*   **File:** `WavyAudio/Services/AppVolumeManager.swift`
*   **Problem:** The entire class is based on the incorrect assumption that macOS provides native, `AVAudioSession`-style APIs for controlling the volume of other applications. The code correctly identifies this limitation and falls back to controlling the global system volume, which does not match the feature's intent. The methods `getApplicationVolume` and `setApplicationVolume` are stubbed out to throw a `notSupported` error.
*   **Impact:** This feature is non-functional and the class is misleading. Any UI built on top of this service will fail to work as expected.
*   **Official Solution:** Apple's documentation for macOS 14.2 and later explicitly provides the **`CATap` API** (`CoreAudio/capturing_system_audio_with_core_audio_taps`) for this exact purpose. This allows an application to capture and process the audio from specific applications, enabling true per-app volume control.

### 2. Architectural Risk: Coupled UI and Audio Engine

*   **Problem:** The current project structure suggests the Rust audio engine (`WavyCore`) is launched as a subprocess of the main Swift UI application.
*   **Impact:** This creates a single point of failure. A crash in the SwiftUI front-end (due to UI bugs, memory pressure, etc.) will terminate the audio engine, causing a catastrophic failure for the user (e.g., their stream or recording will instantly go dead).
*   **Official Solution:** For maximum stability, the audio engine should be architected as a separate background service using `launchd`. The UI application would then act as a remote control, communicating with the daemon via a stable IPC mechanism like XPC.

### 3. Runtime Failure Risk: Missing `Info.plist` Key

*   **File:** `WavyAudio/Info.plist`
*   **Problem:** The `Info.plist` is missing the `NSAudioCaptureUsageDescription` key.
*   **Impact:** The moment the code attempts to use the `CATap` API (the correct solution for issue #1), the application will be immediately terminated by the OS for not having the required privacy usage description. This is a guaranteed runtime crash.

### 4. Future-Proofing Risk: Outdated Driver Model

*   **Problem:** The project appears to use the traditional `.driver` model for its virtual "Ark" device, which is placed in `/Library/Audio/Plug-Ins/HAL`.
*   **Impact:** While this model is still functional, Apple's documentation for macOS 12+ points towards a newer **Audio Server Plug-in and Driver Extension** model using DriverKit. The legacy HAL plug-in model may be deprecated in future macOS versions, leading to build or compatibility failures.
*   **Official Solution:** A long-term roadmap item should be to migrate the virtual device from a HAL plug-in to a modern DriverKit extension.

### 5. Audio Glitch Risk: Lack of Real-time Resampling

*   **Problem:** The project lacks an explicit, real-time audio resampling mechanism.
*   **Impact:** If a user creates a route between two devices operating at different sample rates (e.g., a 44.1kHz microphone and a 48kHz interface), Core Audio may fail to establish the connection, or it may introduce audio glitches, clicks, and pops.
*   **Solution:** A high-quality, low-latency resampling library should be integrated into the Rust audio engine to be automatically inserted into routes where a sample rate mismatch is detected.
