# WavyAudio: Architecture Fix and Feature Implementation Plan

This document outlines the official plan to address the critical architectural issues identified in `ARCHITECTURAL_ISSUES.md` and provides a concrete implementation path for the flagship per-application volume control feature.

## 1. Addressing Critical Issues

### Issue #1 & #3: Flawed Volume Manager and Missing Permissions

*   **Action Taken:**
    1.  The incorrect `AppVolumeManager.swift` has been deleted from the project.
    2.  The required `NSAudioCaptureUsageDescription` key has been added to `WavyAudio/Info.plist` to enable the correct implementation.
*   **Next Step:** Implement the `ProcessTapManager` as detailed below.

### Issue #2: Coupled UI and Audio Engine

*   **Action Plan:** This is a major architectural change that should be a top priority for the next development cycle.
    1.  Refactor the `WavyCore` Rust engine to be built as a standalone binary.
    2.  Create a `launchd.plist` to register `WavyCore` as a background service that starts on login.
    3.  Implement an XPC service definition for communication between the Swift UI and the Rust daemon. This will be the new contract for sending commands (e.g., "create route," "set volume") and receiving status updates.

## 2. New Feature Implementation: Per-Application Volume Mixer

This feature will be implemented using Apple's modern `CATap` API.

### UI Integration

A new "App Mixer" view will be created. It will display a list of all applications currently playing audio, each with its own:
*   Volume Slider
*   Mute Button
*   Real-time peak volume meter

### Technical Implementation

**Step 1: Create a `ProcessTapManager`**

This new class will manage the lifecycle of audio taps for each application.

```swift
// In a new file: WavyAudio/Services/ProcessTapManager.swift
import Foundation
import CoreAudio
import AVFoundation
import Combine

@available(macOS 14.2, *)
class ProcessTapManager: ObservableObject {
    @Published private(set) var tappedProcesses: [pid_t: ProcessAudioTap] = [:]
    private var processMonitor: Any? // Opaque type for process monitor

    func tapProcess(pid: pid_t, name: String) {
        guard tappedProcesses[pid] == nil else { return }

        let newTap = ProcessAudioTap(processID: pid, name: name)
        do {
            try newTap.start()
            DispatchQueue.main.async {
                self.tappedProcesses[pid] = newTap
            }
        } catch {
            print("Error starting tap for \(name) (\(pid)): \(error)")
        }
    }

    func removeTap(for pid: pid_t) {
        tappedProcesses[pid]?.stop()
        DispatchQueue.main.async {
            self.tappedProcesses.removeValue(forKey: pid)
        }
    }
    
    // In a real implementation, a process monitor (e.g., using KQueue or AppKit's NSWorkspace)
    // would be needed to automatically detect when audio-producing apps launch and quit.
}
```

**Step 2: Implement the `ProcessAudioTap`**

This class encapsulates a single tap and its associated `AVAudioEngine` for volume control.

```swift
// In the same file: WavyAudio/Services/ProcessTapManager.swift

@available(macOS 14.2, *)
class ProcessAudioTap: ObservableObject, Identifiable {
    let id: pid_t
    let name: String
    
    @Published var volume: Float = 1.0 {
        didSet {
            audioEngine.mainMixerNode.outputVolume = volume
        }
    }
    @Published var isMuted: Bool = false {
        didSet {
            audioEngine.mainMixerNode.outputVolume = isMuted ? 0.0 : volume
        }
    }

    private var tapID: AudioObjectID = kAudioObjectUnknown
    private let audioEngine = AVAudioEngine()
    private var sourceNode: AVAudioSourceNode!

    init(processID: pid_t, name: String) {
        self.id = processID
        self.name = name
    }

    func start() throws {
        // 1. Create the Tap Description
        let description = CATapDescription()
        description.name = "WavyAudio Tap for \(name)"
        description.processes = [NSNumber(value: id)]
        description.muteBehavior = .mute // Mute the original process output so we can control it

        // 2. Create the Tap
        var newTapID = AudioObjectID(kAudioObjectUnknown)
        let status = AudioHardwareCreateProcessTap(description, &newTapID)
        guard status == noErr else { throw NSError(domain: NSOSStatusErrorDomain, code: Int(status), userInfo: nil) }
        self.tapID = newTapID

        // 3. Set up AVAudioEngine to process the tap's audio
        // Note: A robust implementation requires a more complex setup to get the
        // tap's format and bridge the Core Audio callback to AVAudioEngine.
        // This is a simplified representation of the concept.
        let tapAudioUnit = try getTapAudioUnit()
        let tapFormat = try getAudioUnitStreamFormat(audioUnit: tapAudioUnit)

        self.sourceNode = AVAudioSourceNode(format: tapFormat) { _, _, frameCount, audioBufferList -> OSStatus in
            var timeStamp = AudioTimeStamp()
            var flags: AudioUnitRenderActionFlags = []
            return AudioUnitRender(tapAudioUnit, &flags, &timeStamp, 0, frameCount, audioBufferList)
        }
        
        audioEngine.attach(sourceNode)
        audioEngine.connect(sourceNode, to: audioEngine.mainMixerNode, format: tapFormat)
        audioEngine.connect(audioEngine.mainMixerNode, to: audioEngine.outputNode, format: nil)

        try audioEngine.start()
    }

    func stop() {
        audioEngine.stop()
        if tapID != kAudioObjectUnknown {
            AudioHardwareDestroyProcessTap(tapID)
            tapID = kAudioObjectUnknown
        }
    }
    
    private func getTapAudioUnit() throws -> AudioUnit {
        var address = AudioObjectPropertyAddress(mSelector: kAudioTapPropertyAudioUnit, mScope: kAudioObjectPropertyScopeGlobal, mElement: kAudioObjectPropertyElementMain)
        var audioUnit: AudioUnit?
        var size = UInt32(MemoryLayout<AudioUnit?>.size)
        let status = AudioObjectGetPropertyData(tapID, &address, 0, nil, &size, &audioUnit)
        guard status == noErr, let unit = audioUnit else {
            throw NSError(domain: NSOSStatusErrorDomain, code: Int(status), userInfo: nil)
        }
        return unit
    }

    private func getAudioUnitStreamFormat(audioUnit: AudioUnit) throws -> AVAudioFormat {
        var streamFormat = AudioStreamBasicDescription()
        var size = UInt32(MemoryLayout<AudioStreamBasicDescription>.size)
        let status = AudioUnitGetProperty(audioUnit, kAudioUnitProperty_StreamFormat, kAudioUnitScope_Output, 0, &streamFormat, &size)
        guard status == noErr, let format = AVAudioFormat(streamDescription: &streamFormat) else {
            throw NSError(domain: NSOSStatusErrorDomain, code: Int(status), userInfo: nil)
        }
        return format
    }
}
