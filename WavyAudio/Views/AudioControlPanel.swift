import SwiftUI
import AVFoundation

struct AudioControlPanel: View {
    @Environment(AudioEngineManager.self) private var audioEngine
    @State private var selectedTab = 0
    @State private var isExpanded = true
    @State private var selectedDevice: AudioDevice?
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with collapse/expand control
            HStack {
                Text("Audio Controls")
                    .font(.headline)
                
                Spacer()
                
                Button(action: {
                    withAnimation(.spring()) {
                        isExpanded.toggle()
                    }
                }) {
                    Image(systemName: isExpanded ? "chevron.down" : "chevron.up")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .buttonStyle(.plain)
            }
            .padding()
            .background(Color(NSColor.controlBackgroundColor))
            .onTapGesture {
                withAnimation(.spring()) {
                    isExpanded.toggle()
                }
            }
            
            // Content
            if isExpanded {
                VStack(spacing: 0) {
                    // Tab selector
                    Picker("", selection: $selectedTab) {
                        Label("Devices", systemImage: "speaker.wave.2")
                            .tag(0)
                        Label("Effects", systemImage: "slider.horizontal.3")
                            .tag(1)
                        Label("Routing", systemImage: "point.3.connected.trianglepath.dotted")
                            .tag(2)
                    }
                    .pickerStyle(.segmented)
                    .labelsHidden()
                    .padding()
                    
                    // Tab content
                    TabView(selection: $selectedTab) {
                        DeviceListView(selectedDevice: $selectedDevice)
                            .tag(0)
                            .tabItem { Label("Devices", systemImage: "speaker.wave.2") }
                        
                        AudioProcessingView()
                            .environment(audioEngine)
                            .tag(1)
                            .tabItem { Label("Effects", systemImage: "slider.horizontal.3") }
                        
                        AudioRoutingView()
                            .tag(2)
                            .tabItem { Label("Routing", systemImage: "point.3.connected.trianglepath.dotted") }
                    }
                    .tabViewStyle(.page(indexDisplayMode: .never))
                    .animation(.easeInOut, value: selectedTab)
                }
                .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
        .background(Color(NSColor.windowBackgroundColor))
        .cornerRadius(8)
        .shadow(radius: 2)
    }
}

// MARK: - Supporting Views

private struct DeviceListView: View {
    @Environment(DeviceManager.self) private var deviceManager
    @Binding var selectedDevice: AudioDevice?
    
    var body: some View {
        List(selection: $selectedDevice) {
            ForEach(deviceManager.devices) { device in
                DeviceRowView(device: device, isSelected: selectedDevice?.id == device.id, conflicts: [])
                    .tag(device as AudioDevice?)
            }
        }
        .listStyle(.plain)
    }
}

private struct AudioRoutingView: View {
    var body: some View {
        VStack {
            Text("Audio Routing")
                .font(.headline)
            
            // Placeholder for routing controls
            // This would be implemented with actual routing logic
            VStack(spacing: 12) {
                HStack {
                    Text("Input")
                    Spacer()
                    Picker("", selection: .constant(0)) {
                        Text("Built-in Microphone").tag(0)
                        Text("External Microphone").tag(1)
                    }
                    .frame(width: 200)
                }
                
                HStack {
                    Text("Output")
                    Spacer()
                    Picker("", selection: .constant(0)) {
                        Text("Built-in Speakers").tag(0)
                        Text("Headphones").tag(1)
                        Text("AirPlay").tag(2)
                    }
                    .frame(width: 200)
                }
            }
            .padding()
            
            Spacer()
        }
        .padding()
    }
}

// MARK: - Preview

#Preview {
    let engine = AudioEngineManager()
    let deviceManager = DeviceManager()
    
    return AudioControlPanel()
        .environment(engine)
        .environment(deviceManager)
        .frame(width: 320, height: 400)
        .padding()
}
