import SwiftUI
import AVFoundation

struct AudioProcessingView: View {
    let audioEngine: AudioEngineManager
    @State private var isProcessingEnabled = true
    @State private var selectedEffect: AudioEffect = .none
    @State private var effectIntensity: Float = 0.5
    
    enum AudioEffect: String, CaseIterable, Identifiable {
        case none = "None"
        case reverb = "Reverb"
        case delay = "Delay"
        case distortion = "Distortion"
        case equalizer = "Equalizer"
        
        var id: String { self.rawValue }
        var icon: String {
            switch self {
            case .none: return "speaker"
            case .reverb: return "waveform.path"
            case .delay: return "time"
            case .distortion: return "waveform.path.badge.minus"
            case .equalizer: return "slider.horizontal.3"
            }
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                Text("Audio Processing")
                    .font(.headline)
                
                Spacer()
                
                Toggle("Enable Processing", isOn: $isProcessingEnabled)
                    .toggleStyle(.switch)
                    .onChange(of: isProcessingEnabled) { newValue in
                        Task {
                            if newValue {
                                await audioEngine.initializeEngine()
                            } else {
                                audioEngine.stopEngine()
                            }
                        }
                    }
            }
            
            // Effect Selection
            VStack(alignment: .leading, spacing: 8) {
                Text("Audio Effect")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Picker("Effect", selection: $selectedEffect) {
                    ForEach(AudioEffect.allCases) { effect in
                        Label(effect.rawValue, systemImage: effect.icon)
                            .tag(effect)
                    }
                }
                .pickerStyle(.segmented)
                .disabled(!isProcessingEnabled)
            }
            
            // Effect Controls
            if selectedEffect != .none {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Effect Intensity")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Slider(
                        value: $effectIntensity,
                        in: 0...1,
                        step: 0.1,
                        onEditingChanged: { _ in
                            // Update effect parameters
                            updateEffectParameters()
                        }
                    )
                    .disabled(!isProcessingEnabled)
                    
                    HStack {
                        Text("0%")
                        Spacer()
                        Text("100%")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
            }
            
            // Performance Metrics
            VStack(alignment: .leading, spacing: 8) {
                Text("Performance")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                HStack {
                    MetricView(
                        title: "Latency",
                        value: String(format: "%.1f", audioEngine.performanceMetrics.audioLatency * 1000),
                        unit: "ms",
                        color: audioEngine.performanceMetrics.audioLatency > 0.01 ? .orange : .green
                    )
                    
                    Divider()
                        .frame(height: 40)
                    
                    MetricView(
                        title: "CPU",
                        value: String(format: "%.1f", audioEngine.performanceMetrics.cpuUsage),
                        unit: "%",
                        color: audioEngine.performanceMetrics.cpuUsage > 70 ? .orange : .green
                    )
                    
                    Divider()
                        .frame(height: 40)
                    
                    MetricView(
                        title: "Memory",
                        value: String(format: "%.1f", audioEngine.performanceMetrics.memoryUsage),
                        unit: "MB",
                        color: .blue
                    )
                }
                .frame(height: 60)
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
        .onAppear {
            // Initialize with current engine state
            isProcessingEnabled = audioEngine.isRunning
        }
    }
    
    private func updateEffectParameters() {
        // Update the audio engine with new effect parameters
        // This would be implemented based on your specific audio processing needs
        audioEngine.updateEffectParameters(intensity: Double(effectIntensity), effect: selectedEffect.rawValue)
    }
}

// MARK: - Supporting Views

private struct MetricView: View {
    let title: String
    let value: String
    let unit: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .center, spacing: 2) {
            HStack(alignment: .firstTextBaseline, spacing: 2) {
                Text(value)
                    .font(.system(.body, design: .rounded).monospacedDigit())
                    .foregroundColor(color)
                
                Text(unit)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - Preview

#Preview {
    let engine = AudioEngineManager()
    return AudioProcessingView(audioEngine: engine)
        .frame(width: 300)
        .padding()
}
