//
// SystemConflict.swift - System Audio Conflict Detection
// WWDC Compliance Reference:
// - WWDC22-110363: Audio Workgroups (System audio cooperation)
// - WWDC19-508: Modernizing Your Audio App (Professional app integration)
//

import Foundation
import CoreAudio
import SwiftUI

// MARK: - System Conflict Types

enum ConflictType: String, CaseIterable, Codable {
    case arkDaemon = "ARK Daemon"
    case soundSource = "SoundSource"
    case otherAudioApp = "Other Audio App"
    case sampleRateMismatch = "Sample Rate Mismatch"
    case exclusiveAccess = "Exclusive Access"
    case bufferSizeConflict = "Buffer Size Conflict"
    case driverIssue = "Driver Issue"
    case systemOverload = "System Overload"
    case multipleInputSources = "Multiple Input Sources"
    case multipleAudioApps = "Multiple Audio Apps"
    case unsignedDrivers = "Unsigned Drivers"
    case spatialAudioConflict = "Spatial Audio Conflict"
    case driverCompatibilityIssue = "Driver Compatibility Issue"
    case audioSessionConflict = "Audio Session Conflict"
    case threadPriorityIssue = "Thread Priority Issue"
    case clockDomainConflict = "Clock Domain Conflict"
}

enum ConflictSeverity: String, CaseIterable, Codable {
    case info = "Info"
    case warning = "Warning"
    case errorLevel = "Error"
    case critical = "Critical"
    
    var color: Color {
        switch self {
        case .info: return .blue
        case .warning: return .orange
        case .errorLevel: return .red
        case .critical: return .purple
        }
    }
    
    var iconName: String {
        switch self {
        case .info: return "info.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .errorLevel: return "xmark.circle.fill"
        case .critical: return "exclamationmark.octagon.fill"
        }
    }
    
    var priority: Int {
        switch self {
        case .info: return 0
        case .warning: return 1
        case .errorLevel: return 2
        case .critical: return 3
        }
    }
}

// MARK: - System Conflict Model

struct SystemConflict: Identifiable, Codable, Equatable {
    let id: UUID
    let type: ConflictType
    let description: String
    let affectedDevices: [AudioDeviceID]
    let severity: ConflictSeverity
    let timestamp: Date
    let details: [String: String]
    
    init(
        id: UUID = UUID(),
        type: ConflictType,
        description: String,
        affectedDevices: [AudioDeviceID] = [],
        severity: ConflictSeverity,
        details: [String: String] = [:]
    ) {
        self.id = id
        self.type = type
        self.description = description
        self.affectedDevices = affectedDevices
        self.severity = severity
        self.timestamp = Date()
        self.details = details
    }
    
    // MARK: - Computed Properties
    
    var displayTitle: String {
        return type.rawValue
    }
    
    var isResolvable: Bool {
        switch type {
        case .arkDaemon, .soundSource, .otherAudioApp:
            return true
        case .sampleRateMismatch, .bufferSizeConflict:
            return true
        case .exclusiveAccess, .driverIssue:
            return false
        case .systemOverload:
            return true
        case .multipleInputSources, .multipleAudioApps:
            return true
        case .unsignedDrivers, .spatialAudioConflict:
            return false
        case .driverCompatibilityIssue:
            return false
        case .audioSessionConflict:
            return true
        case .threadPriorityIssue:
            return true
        case .clockDomainConflict:
            return false
        }
    }
    
    var recommendedAction: String {
        switch type {
        case .arkDaemon:
            return "Disable ARK daemon or configure audio routing to work cooperatively"
        case .soundSource:
            return "Enable SoundSource compatibility mode in WavyAudio settings"
        case .otherAudioApp:
            return "Close other audio applications or configure shared access"
        case .sampleRateMismatch:
            return "Adjust sample rate to match other audio devices"
        case .exclusiveAccess:
            return "Grant exclusive access to audio device or switch to shared mode"
        case .bufferSizeConflict:
            return "Adjust buffer size to match system requirements"
        case .driverIssue:
            return "Update audio driver or contact manufacturer"
        case .systemOverload:
            return "Reduce audio processing load or increase buffer size"
        case .multipleInputSources:
            return "Configure input source priority or disable unused inputs"
        case .multipleAudioApps:
            return "Close competing audio applications or enable audio sharing"
        case .unsignedDrivers:
            return "Install properly signed audio drivers from official sources"
        case .spatialAudioConflict:
            return "Disable spatial audio or configure compatible audio output"
        case .driverCompatibilityIssue:
            return "Update to compatible driver version or contact manufacturer"
        case .audioSessionConflict:
            return "Restart audio session or configure session sharing"
        case .threadPriorityIssue:
            return "Adjust audio thread priority or reduce system load"
        case .clockDomainConflict:
            return "Synchronize audio clocks or use single clock domain"
        }
    }
    
    var canAutoResolve: Bool {
        switch type {
        case .soundSource, .sampleRateMismatch, .bufferSizeConflict:
            return true
        case .multipleInputSources, .audioSessionConflict, .threadPriorityIssue:
            return true
        default:
            return false
        }
    }
}

// MARK: - System Conflict Extensions

extension SystemConflict {
    static func arkDaemonConflict() -> SystemConflict {
        return SystemConflict(
            type: .arkDaemon,
            description: "ARK daemon detected - audio routing may be affected. Consider disabling ARK or enabling compatibility mode.",
            severity: .warning,
            details: [
                "process": "ARK Audio Daemon",
                "impact": "May interfere with audio routing",
                "solution": "Disable ARK or enable compatibility mode"
            ]
        )
    }
    
    static func soundSourceConflict() -> SystemConflict {
        return SystemConflict(
            type: .soundSource,
            description: "SoundSource detected - enabling compatibility mode for optimal performance.",
            severity: .info,
            details: [
                "process": "SoundSource",
                "impact": "Shared audio device access",
                "solution": "Compatibility mode enabled automatically"
            ]
        )
    }
    
    static func sampleRateConflict(deviceID: AudioDeviceID, expectedRate: Double, actualRate: Double) -> SystemConflict {
        return SystemConflict(
            type: .sampleRateMismatch,
            description: "Sample rate mismatch detected: expected \(expectedRate)Hz, got \(actualRate)Hz",
            affectedDevices: [deviceID],
            severity: .warning,
            details: [
                "expectedRate": "\(expectedRate)",
                "actualRate": "\(actualRate)",
                "deviceID": "\(deviceID)"
            ]
        )
    }
    
    static func exclusiveAccessConflict(deviceID: AudioDeviceID, conflictingApp: String) -> SystemConflict {
        return SystemConflict(
            type: .exclusiveAccess,
            description: "Device is in use by \(conflictingApp) - exclusive access required",
            affectedDevices: [deviceID],
            severity: .errorLevel,
            details: [
                "conflictingApp": conflictingApp,
                "deviceID": "\(deviceID)",
                "accessMode": "exclusive"
            ]
        )
    }
}

// MARK: - Conflict Resolution

extension SystemConflict {
    func resolveAutomatically() -> Bool {
        guard canAutoResolve else { return false }
        
        switch type {
        case .soundSource:
            // Enable SoundSource compatibility mode
            UserDefaults.standard.set(true, forKey: "soundSourceCompatibilityMode")
            return true
            
        case .sampleRateMismatch:
            // Attempt to adjust sample rate
            if let deviceIDString = details["deviceID"],
               let deviceID = AudioDeviceID(deviceIDString),
               let expectedRateString = details["expectedRate"],
               let expectedRate = Double(expectedRateString) {
                return adjustSampleRate(deviceID: deviceID, targetRate: expectedRate)
            }
            return false
            
        case .bufferSizeConflict:
            // Attempt to adjust buffer size
            UserDefaults.standard.set(true, forKey: "autoAdjustBufferSize")
            return true
            
        default:
            return false
        }
    }
    
    private func adjustSampleRate(deviceID: AudioDeviceID, targetRate: Double) -> Bool {
        // Core Audio implementation to adjust sample rate
        var propertyAddress = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyNominalSampleRate,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        var sampleRate = targetRate
        let dataSize = UInt32(MemoryLayout<Float64>.size)
        
        let status = AudioObjectSetPropertyData(
            deviceID,
            &propertyAddress,
            0,
            nil,
            dataSize,
            &sampleRate
        )
        
        return status == noErr
    }
}