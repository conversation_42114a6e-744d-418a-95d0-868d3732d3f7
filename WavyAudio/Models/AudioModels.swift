
import Foundation
import CoreAudio
import SwiftUI



// MARK: - Audio Device Status

enum AudioDeviceStatus: String, CaseIterable {
    case active = "Active"
    case inactive = "Inactive"
    case error = "Error"
    case connecting = "Connecting"
    case disconnected = "Disconnected"
    
    var color: Color {
        switch self {
        case .active: return .green
        case .inactive: return .gray
        case .error: return .red
        case .connecting: return .orange
        case .disconnected: return .gray
        }
    }
    
    var icon: String {
        switch self {
        case .active: return "checkmark.circle.fill"
        case .inactive: return "pause.circle"
        case .error: return "exclamationmark.triangle.fill"
        case .connecting: return "arrow.clockwise"
        case .disconnected: return "xmark.circle"
        }
    }
}



// MARK: - Audio Device Collection Extensions

extension Array where Element == AudioDevice {
    
    // Filter devices by type
    func devices(ofType type: AudioDeviceType) -> [AudioDevice] {
        return filter { $0.type == type }
    }
    
    // Filter devices by capability
    func inputDevices() -> [AudioDevice] {
        return filter { $0.canRecord }
    }
    
    func outputDevices() -> [AudioDevice] {
        return filter { $0.canPlayback }
    }
    
    func virtualDevices() -> [AudioDevice] {
        return filter { $0.isVirtual }
    }
    
    func aggregateDevices() -> [AudioDevice] {
        return filter { $0.isAggregate }
    }
    
    // Find device by ID
    func device(withID deviceID: AudioDeviceID) -> AudioDevice? {
        return first { $0.deviceID == deviceID }
    }
    
    // Sort devices by various criteria
    func sortedByName() -> [AudioDevice] {
        return sorted { $0.name.localizedCaseInsensitiveCompare($1.name) == .orderedAscending }
    }
    
    func sortedByType() -> [AudioDevice] {
        return sorted(by: { $0.type.rawValue < $1.type.rawValue })
    }
    
    func sortedByChannelCount() -> [AudioDevice] {
        return sorted { ($0.inputChannels + $0.outputChannels) > ($1.inputChannels + $1.outputChannels) }
    }
}






// MARK: - Channel Mapping

struct ChannelMapping: Identifiable, Hashable, Codable {
    let id: UUID
    let sourceChannel: Int
    let destinationChannel: Int
    var gain: Float
    var isEnabled: Bool

    nonisolated enum CodingKeys: String, CodingKey {
        case id, sourceChannel, destinationChannel, gain, isEnabled
    }

    @preconcurrency public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        sourceChannel = try container.decode(Int.self, forKey: .sourceChannel)
        destinationChannel = try container.decode(Int.self, forKey: .destinationChannel)
        gain = try container.decode(Float.self, forKey: .gain)
        isEnabled = try container.decode(Bool.self, forKey: .isEnabled)
    }

    @preconcurrency public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(sourceChannel, forKey: .sourceChannel)
        try container.encode(destinationChannel, forKey: .destinationChannel)
        try container.encode(gain, forKey: .gain)
        try container.encode(isEnabled, forKey: .isEnabled)
    }
    
    init(
        id: UUID = UUID(),
        sourceChannel: Int,
        destinationChannel: Int,
        gain: Float = 1.0,
        isEnabled: Bool = true
    ) {
        self.id = id
        self.sourceChannel = sourceChannel
        self.destinationChannel = destinationChannel
        self.gain = gain
        self.isEnabled = isEnabled
    }
}

// MARK: - Channel Mapping Group

struct ChannelMappingGroup: Identifiable, Hashable, Codable {
    let id: UUID
    let name: String
    var mappings: [ChannelMapping]

    nonisolated enum CodingKeys: String, CodingKey {
        case id, name, mappings
    }

    @preconcurrency public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        mappings = try container.decode([ChannelMapping].self, forKey: .mappings)
    }

    @preconcurrency public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(mappings, forKey: .mappings)
    }
    
    init(
        id: UUID = UUID(),
        name: String,
        mappings: [ChannelMapping]
    ) {
        self.id = id
        self.name = name
        self.mappings = mappings
    }
    
    // Add or remove mappings
    mutating func addMapping(_ mapping: ChannelMapping) {
        mappings.append(mapping)
    }
    
    mutating func removeMapping(at index: Int) {
        mappings.remove(at: index)
    }
    
    // Enable or disable all mappings
    mutating func setAllEnabled(_ enabled: Bool) {
        for i in 0..<mappings.count {
            mappings[i].isEnabled = enabled
        }
    }
}

// MARK: - Channel Mapping Presets

struct ChannelMappingPreset: Identifiable, Hashable, Codable {
    let id: UUID
    let name: String
    let description: String
    let mappingGroup: ChannelMappingGroup

    nonisolated enum CodingKeys: String, CodingKey {
        case id, name, description, mappingGroup
    }

    @preconcurrency public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        description = try container.decode(String.self, forKey: .description)
        mappingGroup = try container.decode(ChannelMappingGroup.self, forKey: .mappingGroup)
    }

    @preconcurrency public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(description, forKey: .description)
        try container.encode(mappingGroup, forKey: .mappingGroup)
    }
    
    init(
        id: UUID = UUID(),
        name: String,
        description: String,
        mappingGroup: ChannelMappingGroup
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.mappingGroup = mappingGroup
    }
    
    // Example presets
    static func defaultPresets() -> [ChannelMappingPreset] {
        return [
            ChannelMappingPreset(
                name: "Stereo to Stereo",
                description: "Standard 1-to-1 stereo mapping",
                mappingGroup: ChannelMappingGroup(
                    name: "Stereo",
                    mappings: [
                        ChannelMapping(sourceChannel: 1, destinationChannel: 1),
                        ChannelMapping(sourceChannel: 2, destinationChannel: 2)
                    ]
                )
            ),
            ChannelMappingPreset(
                name: "Mono Sum",
                description: "Sums stereo channels to mono",
                mappingGroup: ChannelMappingGroup(
                    name: "Mono",
                    mappings: [
                        ChannelMapping(sourceChannel: 1, destinationChannel: 1, gain: 0.5),
                        ChannelMapping(sourceChannel: 2, destinationChannel: 1, gain: 0.5)
                    ]
                )
            )
        ]
    }
}
