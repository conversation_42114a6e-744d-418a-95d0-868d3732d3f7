//
// AudioEngineManager.swift - Core Audio Engine Management
// WWDC Compliance Reference:
// - WWDC19-508: Modernizing Your Audio App (Deprecated APIs: AUGraph → AVAudioEngine)
// - WWDC25-251: AVFoundation advances (AVAudioSession improvements)
// - WWDC25-268: Swift 6 concurrency (MainActor, @Observable patterns)
// - WWDC21-10036: Sound Analysis (Real-time audio processing)
//
// Status: FIXED - Restored proper Swift class structure following WWDC19-508 guidance
// WWDC Research Tool: python3 /Users/<USER>/wwdc-transcript-tool.py --session 2019-508
//

import Foundation
import CoreAudio
import AVFoundation
import Metal
import Observation
import OSLog

@Observable
@MainActor
class AudioEngineManager {
    private(set) var engineStatus: EngineStatus = .stopped
    private(set) var availableDevices: [AudioDevice] = []
    private(set) var activeConnections: [AudioConnection] = []
    private(set) var performanceMetrics: AudioEnginePerformanceMetrics = AudioEnginePerformanceMetrics()
    
    private var audioEngine: AVAudioEngine?
    
    // Combined isRunning property that checks both engine status and audioEngine
    var isRunning: Bool {
        if case .running = engineStatus {
            return audioEngine?.isRunning ?? false
        }
        return false
    }
    
    var hasConflicts: Bool {
        // Simple implementation - could be enhanced based on CoreAudioHALManager
        return !systemConflicts.isEmpty
    }

    var hasEngineError: Bool {
        if case .error(_) = engineStatus {
            return true
        }
        return false
    }

    var engineErrorMessage: String {
        if case .error(let message) = engineStatus {
            return message
        }
        return ""
    }

    // Placeholder for system conflicts reported by HAL manager
    var systemConflicts: [SystemConflict] {
        // Use synchronous call to avoid async in computed property
        return CoreAudioHALManager.shared.detectSystemConflictsSynchronously()
    }
    
    private var metalViewModel = MetalViewModel()
    private let logger = Logger(subsystem: "com.wavyaudio.core", category: "AudioEngineManager")

    enum EngineStatus {
        case stopped, initializing, running, error(String)
    }

    init() {
        setupDeviceNotifications()
    }

    func initializeEngine() async {
        guard await AVAudioApplication.requestRecordPermission() else {
            engineStatus = .error("Permission denied")
            return
        }
        
        engineStatus = .initializing
        do {
            try await initializeCoreAudioHAL()
            await refreshDevices()
            engineStatus = .running
        } catch {
            engineStatus = .error("Failed to start engine: \(error.localizedDescription)")
        }
    }
    
    func stopEngine() {
        engineStatus = .stopped
        CoreAudioHALManager.shared.cleanup()
        activeConnections.removeAll()
    }

    func clearAllConnections() async {
        activeConnections.removeAll()
        logger.info("Cleared all audio connections")
    }
    
    func refreshDevices() async {
        availableDevices = await withUnsafeContinuation { continuation in
            Task {
                let devices = CoreAudioHALManager.shared.enumerateDevices()
                let audioDevices = devices.map { mapDevice($0) }
                continuation.resume(returning: audioDevices)
            }
        }
    }
    
    // MARK: - Core Audio HAL Initialization
    
    private func initializeCoreAudioHAL() async throws {
        do {
            try await CoreAudioHALManager.shared.initialize()
        } catch {
            self.logger.error("Core Audio HAL initialization failed: \(error.localizedDescription)")
            throw error
        }
    }
    
    // MARK: - Audio Device Management
    
    private func getInputChannelCount(deviceID: AudioDeviceID) -> Int {
        getChannelCount(deviceID: deviceID, scope: kAudioDevicePropertyScopeInput)
    }
    
    private func getOutputChannelCount(deviceID: AudioDeviceID) -> Int {
        getChannelCount(deviceID: deviceID, scope: kAudioDevicePropertyScopeOutput)
    }
    
    private func getChannelCount(deviceID: AudioDeviceID, scope: AudioObjectPropertyScope) -> Int {
        var propertyAddress = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyStreams,
            mScope: scope,
            mElement: kAudioObjectPropertyElementMain
        )
        
        var propertySize: UInt32 = 0
        let getPropertySize = AudioObjectGetPropertyDataSize(deviceID, &propertyAddress, 0, nil, &propertySize)
        guard getPropertySize == noErr, propertySize > 0 else {
            self.logger.error("Failed to get property size for scope \(scope)")
            return 0
        }
        
        let streamCount = Int(propertySize) / MemoryLayout<AudioStreamID>.size
        let streamIDs = UnsafeMutablePointer<AudioStreamID>.allocate(capacity: streamCount)
        defer { streamIDs.deallocate() }

        var mutablePropertySize = propertySize
        let getStatus = AudioObjectGetPropertyData(
            deviceID, &propertyAddress, 0, nil,
            &mutablePropertySize, streamIDs
        )
        
        guard getStatus == noErr else {
            self.logger.error("Failed to get stream IDs for scope: \(scope) error:\(getStatus)")
            return 0
        }
        
        var totalChannels = 0
        for i in 0..<streamCount {
            let streamID = streamIDs[i]
            totalChannels += getStreamChannelCount(streamID: streamID)
        }
        
        return totalChannels
    }

    private func getStreamChannelCount(streamID: AudioStreamID) -> Int {
        var propertyAddress = AudioObjectPropertyAddress(
            mSelector: kAudioStreamPropertyPhysicalFormat,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )

        var propertyDataSize: UInt32 = 0
        let getPropertySize = AudioObjectGetPropertyDataSize(streamID, &propertyAddress, 0, nil, &propertyDataSize)
        guard getPropertySize == noErr, propertyDataSize >= MemoryLayout<AudioStreamBasicDescription>.size else {
            self.logger.error("Invalid AudioStreamBasicDescription size \(propertyDataSize)")
            return 0
        }
        
        let asbd = UnsafeMutablePointer<AudioStreamBasicDescription>.allocate(capacity: 1)
        defer { asbd.deallocate() }
        
        var mutablePropertyDataSize = propertyDataSize
        let getStatus = AudioObjectGetPropertyData(
            streamID, &propertyAddress, 0, nil,
            &mutablePropertyDataSize, asbd
        )
        
        guard getStatus == noErr else {
            self.logger.error("Failed to get stream channel count: \(getStatus)")
            return 0
        }
        
        return Int(asbd.pointee.mChannelsPerFrame)
    }
    
    private func mapDevice(_ coreDevice: AudioDeviceInfo) -> AudioDevice {
        AudioDevice(
            id: UUID(),
            deviceID: coreDevice.deviceID,
            name: coreDevice.name,
            manufacturer: coreDevice.manufacturer,
            inputChannels: getInputChannelCount(deviceID: coreDevice.deviceID),
            outputChannels: getOutputChannelCount(deviceID: coreDevice.deviceID),
            sampleRate: coreDevice.sampleRate,
            isVirtual: coreDevice.isVirtual,
            isAggregate: coreDevice.isAggregate
        )
    }
    
    // MARK: - Helper Methods
    
    private func setupDeviceNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleDeviceListChanged),
            name: .audioDeviceListChanged,
            object: nil
        )
    }
    
    @objc private func handleDeviceListChanged() {
        Task { @MainActor in await refreshDevices() }
        self.logger.info("Audio devices list changed")
    }
    
    // MARK: - Cleanup
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Audio Effect Methods
    
    func updateEffectParameters(intensity: Double, effect: String) {
        logger.debug("Updating effect parameters: \(effect) with intensity \(intensity)")
        
        // Update audio effect parameters based on the selected effect
        // This is a placeholder implementation - would be enhanced based on your specific effects
        guard let engine = audioEngine else {
            logger.warning("Cannot update effect parameters - engine not initialized")
            return
        }
        
        // Apply effects to the audio engine
        // Implementation would depend on your specific audio processing needs
    }
}

// MARK: - Supporting Types

struct AudioEnginePerformanceMetrics {
    var cpuUsage: Double = 0.0
    var memoryUsage: Double = 0.0
    var audioLatency: Double = 0.0
    var frameRate: Double = 0.0
    var performanceWarnings: Int = 0
}

enum AudioEngineError: Error, LocalizedError {
    case engineNotInitialized
    case permissionDenied
    case invalidConfiguration
    case halInitializationFailed
    case deviceListenerFailed
    case unhandledError(message: String)
    
    var errorDescription: String? {
        switch self {
        case .engineNotInitialized: return "Audio engine not initialized"
        case .permissionDenied: return "Audio permissions denied"
        case .invalidConfiguration: return "Invalid audio engine configuration"
        case .halInitializationFailed: return "Core Audio HAL initialization failed"
        case .deviceListenerFailed: return "Failed to set up device listener"
        case .unhandledError(let message): return message
        }
    }
}