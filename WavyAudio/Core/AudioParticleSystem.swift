//
// AudioParticleSystem.swift - High-Performance Particle System for Audio Flow
// WWDC Compliance Reference:
// - WWDC2021-10021: Metal Performance Shaders and Particle Systems
// - WWDC2019-608: Metal for Pro Apps (GPU acceleration)
// - WWDC2025-319: SwiftUI Canvas Performance Optimization
//
// Status: METAL OPTIMIZED - Zero-allocation particle system
// WWDC Research Tool: python3 /Users/<USER>/wwdc-transcript-tool.py --session 2021-10021
//

import Foundation
import SwiftUI
import Metal
import OSLog

// MARK: - Audio Particle

class AudioParticle {
    var position: CGPoint = .zero
    var velocity: CGVector = .zero
    var life: Float = 1.0
    var maxLife: Float = 1.0
    var opacity: Float = 1.0
    var size: CGFloat = 4.0
    var color: Color = .blue
    var connectionID: UUID?
    var isActive: Bool = false
    
    func initialize(for connection: AudioConnection, strength: Float) {
        // Initialize particle along connection path
        self.connectionID = connection.id
        self.position = connection.startPoint
        self.life = 1.0
        self.maxLife = 1.0 + strength * 2.0 // Longer life for stronger signals
        self.opacity = strength
        self.size = 2.0 + CGFloat(strength * 4.0)
        self.color = connection.connectionType.color
        self.isActive = true
        
        // Calculate velocity towards end point
        let direction = CGVector(
            dx: connection.endPoint.x - connection.startPoint.x,
            dy: connection.endPoint.y - connection.startPoint.y
        )
        let distance = sqrt(direction.dx * direction.dx + direction.dy * direction.dy)
        
        if distance > 0 {
            let speed = 50.0 + Double(strength * 100.0) // Speed based on signal strength
            self.velocity = CGVector(
                dx: (direction.dx / distance) * speed,
                dy: (direction.dy / distance) * speed
            )
        }
    }
    
    func update(deltaTime: TimeInterval) {
        guard isActive else { return }
        
        // Update position
        position.x += velocity.dx * deltaTime
        position.y += velocity.dy * deltaTime
        
        // Update life
        life -= Float(deltaTime)
        
        // Update opacity based on life
        opacity = life / maxLife
        
        // Deactivate if life is over
        if life <= 0 {
            isActive = false
        }
    }
    
    func reset() {
        position = .zero
        velocity = .zero
        life = 1.0
        maxLife = 1.0
        opacity = 1.0
        size = 4.0
        color = .blue
        connectionID = nil
        isActive = false
    }
}

// MARK: - Audio Particle System

@MainActor
@Observable
class AudioParticleSystem {
    private let logger = Logger(subsystem: "com.wavyaudio.core", category: "AudioParticleSystem")
    
    // Performance configuration
    private let maxParticles = 1000
    private let particlesPerConnection = 20
    private let emissionRate: TimeInterval = 1.0/30.0 // 30 Hz emission
    
    // Pre-allocated particle pool (zero-allocation design)
    private var particlePool: [AudioParticle] = []
    private var activeParticles: [AudioParticle] = []
    private var inactiveParticles: [AudioParticle] = []
    
    // Emission control
    private var lastEmissionTime: TimeInterval = 0
    private var emissionTimer: Timer?
    
    // Performance monitoring
    private var frameTime: TimeInterval = 0
    private var updateCount: Int = 0
    
    // Metal rendering optimization
    private var resolvedImages: [Color: GraphicsContext.ResolvedImage] = [:]
    
    init() {
        setupParticlePool()
        logger.info("AudioParticleSystem initialized with \(self.maxParticles) particles")
    }
    
    deinit {
        logger.info("AudioParticleSystem deinitialized")
    }
    
    // MARK: - Particle Pool Management
    
    private func setupParticlePool() {
        // Pre-allocate all particles to avoid runtime allocations
        particlePool.reserveCapacity(maxParticles)
        inactiveParticles.reserveCapacity(maxParticles)
        activeParticles.reserveCapacity(maxParticles)
        
        for _ in 0..<maxParticles {
            let particle = AudioParticle()
            particlePool.append(particle)
            inactiveParticles.append(particle)
        }
        
        logger.info("Particle pool initialized with \(self.maxParticles) particles")
    }
    
    private func getParticleFromPool() -> AudioParticle? {
        if let particle = inactiveParticles.popLast() {
            particle.reset()
            return particle
        }
        return nil
    }
    
    private func returnParticleToPool(_ particle: AudioParticle) {
        particle.reset()
        
        // Remove from active and add to inactive
        if let index = activeParticles.firstIndex(where: { $0 === particle }) {
            activeParticles.remove(at: index)
        }
        
        inactiveParticles.append(particle)
    }
    
    // MARK: - Particle System Control
    
    func startEmission() {
        guard emissionTimer == nil else { return }
        
        emissionTimer = Timer.scheduledTimer(withTimeInterval: emissionRate, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateActiveParticles(deltaTime: 1.0/30.0)
            }
        }
        
        logger.info("Particle emission started")
    }
    
    func stopEmission() {
        emissionTimer?.invalidate()
        emissionTimer = nil
        
        // Return all active particles to pool
        for particle in activeParticles {
            returnParticleToPool(particle)
        }
        
        logger.info("Particle emission stopped")
    }
    
    // MARK: - Particle Updates
    
    func updateParticles(connections: [AudioConnection], signalData: [Float]) {
        let currentTime = CACurrentMediaTime()
        let deltaTime = currentTime - frameTime
        frameTime = currentTime
        
        // Update existing particles
        updateActiveParticles(deltaTime: deltaTime)
        
        // Emit new particles based on connections
        emitParticlesForConnections(connections, signalData: signalData)
        
        // Performance monitoring
        updateCount += 1
        if updateCount % 60 == 0 {
            logPerformanceMetrics()
        }
    }
    
    private func updateActiveParticles(deltaTime: TimeInterval) {
        var particlesToRemove: [AudioParticle] = []
        
        for particle in activeParticles {
            particle.update(deltaTime: deltaTime)
            
            if !particle.isActive {
                particlesToRemove.append(particle)
            }
        }
        
        // Return inactive particles to pool
        for particle in particlesToRemove {
            returnParticleToPool(particle)
        }
    }
    
    private func emitParticlesForConnections(_ connections: [AudioConnection], signalData: [Float]) {
        for (index, connection) in connections.enumerated() {
            let signalStrength = signalData.indices.contains(index) ? signalData[index] : 0
            
            // Skip if no signal
            guard signalStrength > 0.01 else { continue }
            
            // Calculate particle count based on signal strength
            let particleCount = min(Int(signalStrength * Float(particlesPerConnection)), particlesPerConnection)
            
            // Emit particles
            for _ in 0..<particleCount {
                if let particle = getParticleFromPool() {
                    particle.initialize(for: connection, strength: signalStrength)
                    activeParticles.append(particle)
                }
            }
        }
    }
    
    // MARK: - Rendering
    
    func renderParticles(context: GraphicsContext, optimized: Bool = false) {
        guard !activeParticles.isEmpty else { return }
        
        // Pre-resolve images for better performance
        if resolvedImages.isEmpty {
            setupResolvedImages(context: context)
        }
        
        // Set blend mode for additive effects
        var effectContext = context
        effectContext.blendMode = .screen
        
        // Render particles
        for particle in activeParticles {
            guard particle.isActive else { continue }
            
            // Skip tiny particles when optimized
            if optimized && particle.size < 2.0 { continue }
            
            // Use resolved image if available
            if let resolvedImage = resolvedImages[particle.color] {
                effectContext.opacity = Double(particle.opacity)
                effectContext.draw(resolvedImage, at: particle.position)
            } else {
                // Fallback to drawing circles
                drawParticleCircle(context: effectContext, particle: particle)
            }
        }
    }
    
    private func setupResolvedImages(context: GraphicsContext) {
        let colors: [Color] = [.blue, .green, .orange, .red, .purple, .cyan]
        
        for color in colors {
            let image = Image(systemName: "circle.fill")
                .foregroundStyle(color)
                .font(.system(size: 8))
            
            if let resolvedImage = context.resolve(image as! Image) {
                resolvedImages[color] = resolvedImage
            }
        }
    }
    
    private func drawParticleCircle(context: GraphicsContext, particle: AudioParticle) {
        let particlePath = Path(ellipseIn: CGRect(
            x: particle.position.x - particle.size / 2,
            y: particle.position.y - particle.size / 2,
            width: particle.size,
            height: particle.size
        ))
        
        var mutableContext = context
        mutableContext.opacity = Double(particle.opacity)
        mutableContext.fill(particlePath, with: .color(particle.color))
    }
    
    // MARK: - Performance Monitoring
    
    private func logPerformanceMetrics() {
        let activeCount = activeParticles.count
        let inactiveCount = inactiveParticles.count
        let poolUtilization = Float(activeCount) / Float(maxParticles) * 100
        
        logger.info("Particle System Stats - Active: \(activeCount), Inactive: \(inactiveCount), Pool: \(poolUtilization)%")
    }
    
    // MARK: - Advanced Features
    
    func setEmissionRate(_ rate: TimeInterval) {
        guard rate > 0 else { return }
        
        // Restart timer with new rate
        stopEmission()
        emissionTimer = Timer.scheduledTimer(withTimeInterval: rate, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateActiveParticles(deltaTime: rate)
            }
        }
    }
    
    func getParticleCount() -> (active: Int, inactive: Int) {
        return (activeParticles.count, inactiveParticles.count)
    }
    
    func clearAllParticles() {
        for particle in activeParticles {
            returnParticleToPool(particle)
        }
        activeParticles.removeAll()
    }
    
    // MARK: - Batch Processing
    
    func processBatchUpdate(connections: [AudioConnection], deltaTime: TimeInterval) {
        // Batch update all particles for better performance
        let batchSize = min(activeParticles.count, 100)
        
        for i in stride(from: 0, to: activeParticles.count, by: batchSize) {
            let endIndex = min(i + batchSize, activeParticles.count)
            let batch = Array(activeParticles[i..<endIndex])
            
            // Process batch
            for particle in batch {
                particle.update(deltaTime: deltaTime)
            }
        }
    }
    
    // MARK: - Memory Management
    
    func optimizeMemory() {
        // Remove excess inactive particles if pool is too large
        let optimalInactiveCount = maxParticles / 2
        
        if inactiveParticles.count > optimalInactiveCount {
            let excessCount = inactiveParticles.count - optimalInactiveCount
            for _ in 0..<excessCount {
                if let particle = inactiveParticles.popLast() {
                    if let poolIndex = particlePool.firstIndex(where: { $0 === particle }) {
                        particlePool.remove(at: poolIndex)
                    }
                }
            }
            
            logger.info("Optimized particle pool, removed \(excessCount) excess particles")
        }
    }
}

// MARK: - Extensions for SwiftUI Integration

extension AudioParticleSystem {
    /// Returns current particle positions for debugging
    var debugParticlePositions: [CGPoint] {
        return activeParticles.map { $0.position }
    }
    
    /// Returns particle system statistics
    var statistics: ParticleSystemStats {
        return ParticleSystemStats(
            activeParticles: activeParticles.count,
            inactiveParticles: inactiveParticles.count,
            totalParticles: maxParticles,
            poolUtilization: Float(activeParticles.count) / Float(maxParticles)
        )
    }
}

// MARK: - Statistics

struct ParticleSystemStats {
    let activeParticles: Int
    let inactiveParticles: Int
    let totalParticles: Int
    let poolUtilization: Float
    
    var isHealthy: Bool {
        poolUtilization < 0.9 // Less than 90% utilization
    }
}

// MARK: - Particle Presets

extension AudioParticleSystem {
    static let highPerformancePreset = ParticleSystemPreset(
        maxParticles: 500,
        particlesPerConnection: 10,
        emissionRate: 1.0/60.0
    )
    
    static let balancedPreset = ParticleSystemPreset(
        maxParticles: 1000,
        particlesPerConnection: 20,
        emissionRate: 1.0/30.0
    )
    
    static let highQualityPreset = ParticleSystemPreset(
        maxParticles: 2000,
        particlesPerConnection: 40,
        emissionRate: 1.0/15.0
    )
}

struct ParticleSystemPreset {
    let maxParticles: Int
    let particlesPerConnection: Int
    let emissionRate: TimeInterval
}
