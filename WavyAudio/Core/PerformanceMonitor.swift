//
// PerformanceMonitor.swift - Audio Performance Analysis & Debugging
// Real-time performance monitoring for audio processing pipeline
//

import Foundation
import os.log
import Combine
import Accelerate
import Observation

@MainActor
@Observable
class PerformanceMonitor {
    private let logger = Logger(subsystem: "com.wavyaudio.performance", category: "Monitor")
    
    // Performance metrics
    var audioLatency: TimeInterval = 0.0
    var cpuUsage: Double = 0.0
    var memoryUsage: Double = 0.0
    var bufferUnderruns: Int = 0
    var frameDrops: Int = 0
    var performanceWarnings: [PerformanceWarning] = []
    
    // Real-time analysis
    private var latencyHistory: [TimeInterval] = []
    private var cpuHistory: [Double] = []
    private var analysisTimer: Timer?
    private var isMonitoring: Bool = false
    private var timer: Timer?
    
    // Performance thresholds
    private let maxAcceptableLatency: TimeInterval = 0.010 // 10ms
    private let maxCPUUsage: Double = 80.0 // 80%
    private let maxMemoryUsage: Double = 200.0 // 200MB
    
    struct PerformanceWarning {
        let timestamp: Date
        let type: WarningType
        let message: String
        let severity: Severity
        
        enum WarningType {
            case audioLatency, cpuOverload, memoryLeak, bufferUnderrun, frameSkip
        }
        
        enum Severity {
            case info, warning, critical
        }
    }
    
    init() {
        startMonitoring()
    }
    
    // MARK: - Real-time Monitoring
    
    func startMonitoring() {
        logger.info("Starting performance monitoring")
        
        analysisTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateMetrics()
                self?.analyzePerformance()
            }
        }
    }
    
    func stopMonitoring() {
        analysisTimer?.invalidate()
        analysisTimer = nil
        logger.info("Performance monitoring stopped")
    }
    
    private func updateMetrics() {
        // Update CPU usage
        cpuUsage = getCurrentCPUUsage()
        cpuHistory.append(cpuUsage)
        if cpuHistory.count > 100 { cpuHistory.removeFirst() }
        
        // Update memory usage
        memoryUsage = getCurrentMemoryUsage()
        
        // Check for performance issues
        if cpuUsage > maxCPUUsage {
            addWarning(.cpuOverload, "CPU usage high: \(Int(cpuUsage))%", .warning)
        }
        
        if memoryUsage > maxMemoryUsage {
            addWarning(.memoryLeak, "Memory usage high: \(Int(memoryUsage))MB", .warning)
        }
    }
    
    // MARK: - Audio-Specific Monitoring
    
    func recordAudioLatency(_ latency: TimeInterval) {
        audioLatency = latency
        latencyHistory.append(latency)
        if latencyHistory.count > 100 { latencyHistory.removeFirst() }
        
        if latency > maxAcceptableLatency {
            addWarning(.audioLatency, 
                      "Audio latency high: \(Int(latency * 1000))ms", 
                      latency > maxAcceptableLatency * 2 ? .critical : .warning)
        }
    }
    
    func recordBufferUnderrun() {
        bufferUnderruns += 1
        addWarning(.bufferUnderrun, "Audio buffer underrun detected", .critical)
    }
    
    func recordFrameDrop() {
        frameDrops += 1
        addWarning(.frameSkip, "UI frame dropped", .warning)
    }
    
    // MARK: - Performance Analysis
    
    private func analyzePerformance() {
        // Analyze latency trends
        if latencyHistory.count >= 10 {
            let averageLatency = latencyHistory.suffix(10).reduce(0, +) / 10
            if averageLatency > maxAcceptableLatency * 0.8 {
                logger.warning("Average latency trending high: \(Int(averageLatency * 1000))ms")
            }
        }
        
        // Analyze CPU usage patterns
        if cpuHistory.count >= 10 {
            let recentCPU = cpuHistory.suffix(10)
            let isConstantlyHigh = recentCPU.allSatisfy { $0 > 70.0 }
            if isConstantlyHigh {
                addWarning(.cpuOverload, "Sustained high CPU usage", .critical)
            }
        }
        
        // Clean old warnings (keep last 50)
        if performanceWarnings.count > 50 {
            performanceWarnings = Array(performanceWarnings.suffix(50))
        }
    }
    
    // MARK: - System Metrics
    
    private func getCurrentCPUUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        guard result == KERN_SUCCESS else {
            return 0.0
        }
        
        // Calculate CPU percentage (simplified)
        let totalTime = Double(info.user_time.seconds + info.system_time.seconds)
        return min(100.0, totalTime * 10.0) // Approximate calculation
    }
    
    private func getCurrentMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        guard result == KERN_SUCCESS else {
            return 0.0
        }
        
        return Double(info.resident_size) / 1024.0 / 1024.0 // Convert to MB
    }
    
    // MARK: - Warning Management
    
    private func addWarning(_ type: PerformanceWarning.WarningType, 
                           _ message: String, 
                           _ severity: PerformanceWarning.Severity) {
        let warning = PerformanceWarning(
            timestamp: Date(),
            type: type,
            message: message,
            severity: severity
        )
        
        performanceWarnings.append(warning)
        
        // Log based on severity
        switch severity {
        case .info:
            logger.info("\(message)")
        case .warning:
            logger.warning("\(message)")
        case .critical:
            logger.error("\(message)")
        }
    }
    
    // MARK: - Performance Reports
    
    func generatePerformanceReport() -> PerformanceReport {
        let averageLatency = latencyHistory.isEmpty ? 0 : latencyHistory.reduce(0, +) / Double(latencyHistory.count)
        let averageCPU = cpuHistory.isEmpty ? 0 : cpuHistory.reduce(0, +) / Double(cpuHistory.count)
        let currentMemory = memoryUsage
        
        return PerformanceReport(
            averageLatency: averageLatency,
            averageCPU: averageCPU,
            currentMemory: currentMemory,
            bufferUnderruns: bufferUnderruns,
            frameDrops: frameDrops,
            warningCount: performanceWarnings.count,
            timestamp: Date()
        )
    }
    

    
    func resetCounters() {
        bufferUnderruns = 0
        frameDrops = 0
        performanceWarnings.removeAll()
        latencyHistory.removeAll()
        cpuHistory.removeAll()
        logger.info("Performance counters reset")
    }
    
    deinit {
        // Clean up synchronously to avoid Task in deinit
        isMonitoring = false
        timer?.invalidate()
        timer = nil
        logger.info("PerformanceMonitor deinitialized")
    }
}

// MARK: - Performance Report

struct PerformanceReport {
    let averageLatency: TimeInterval
    let averageCPU: Double
    let currentMemory: Double
    let bufferUnderruns: Int
    let frameDrops: Int
    let warningCount: Int
    let timestamp: Date
    
    var isHealthy: Bool {
        return averageLatency < 0.010 && 
               averageCPU < 50.0 && 
               currentMemory < 100.0 && 
               bufferUnderruns == 0
    }
    
    var performanceGrade: String {
        if isHealthy && warningCount == 0 {
            return "Excellent"
        } else if averageLatency < 0.020 && averageCPU < 70.0 {
            return "Good"
        } else if averageLatency < 0.050 && averageCPU < 85.0 {
            return "Fair"
        } else {
            return "Poor"
        }
    }
}
